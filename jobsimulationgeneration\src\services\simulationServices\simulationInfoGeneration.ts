import { openai } from "@ai-sdk/openai";
import { deepseek } from "@ai-sdk/deepseek";
import { ModelMessage, generateObject, LanguageModel, UserContent } from "ai";
import { z } from "zod";

/**
 * TODO: Dựa vào thông tin 1 job
 * - title: string. required
 * - description: string. required
 * - companyName: string. required
 * - skills: string[]. optional
 * - categories: string[]. optional
 * 
 * Hãy viết hàm generate ra simulation info gồm các thông tin sau. (Viết prompt cho deepseek và openai. Ưu tiên dùng deepseek. Nếu deepseek lỗi thì mới dùng openai)
 * - jobSimulationId: string. required. Unique id cho mỗi job simulation. Dựa vào title của job. jobSimulationId phải ngắn, dễ nhớ, không có khoảng trắng, ký tự đặc biệt, là các chữ in thường, mỗi từ cách nhau bằng dấu gạch ngang.
 * - name: string. required. Tên của job simulation. Dựa vào title và description của job
 * - description: string. required. Dựa vào companyName, title, description của job
 * - username: string. required. Username để login vào job simulation. Dựa vào companyName và title của job. Tên phải ngắn, dễ nhớ, không có khoảng trắng hay ký tự đặc biệt, là các chữ in thường, mỗi từ cách nhau bằng dấu chấm.
 * - password: string. required. Password để login vào job simulation. Dựa vào companyName và title của job. Tên phải ngắn, dễ nhớ, không có khoảng trắng hay ký tự đặc biệt.
 * 
 * Ví dụ:
 * - Input Job:
 * title: 'Cloud Architect (Up to $3500)'
 * description: 'We are looking for a cloud architect to design and implement our cloud infrastructure.'
 * companyName: 'Nimbus Works'
 * - Output:
 * jobSimulationId: 'cloud-architect'
 * name: 'Cloud Architect'
 * description: 'Work as a Cloud Architect at Nimbus Works, exploring how organizations design and manage cloud infrastructure. In this simulation, you'll gain insight into key concepts like scalability, security, and system architecture — helping you understand the role of a Cloud Architect in modern tech environments.'
 * username: 'cloud.nimbuswork.sim'
 * password: 'cloud2025'
 * 
 */